{"name": "imponeer/log-data-output-decorator", "description": "Small decorator that extends Symfony OutputInterface delivered class with few options for easier to log data", "type": "library", "require": {"php": "^8.3", "symfony/console": "^7"}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Imponeer\\Decorators\\LogDataOutput\\": "src/"}}, "autoload-dev": {"psr-4": {"Imponeer\\Decorators\\LogDataOutput\\Tests\\": "tests/"}}, "keywords": ["symfony", "console", "output", "decorator"], "require-dev": {"phpunit/phpunit": "^12", "squizlabs/php_codesniffer": "^3.10", "phpstan/phpstan": "^2"}, "scripts": {"test": "phpunit --testdox", "phpcs": "phpcs --standard=PSR12 src/ tests/", "phpcbf": "phpcbf --standard=PSR12 src/ tests/"}}